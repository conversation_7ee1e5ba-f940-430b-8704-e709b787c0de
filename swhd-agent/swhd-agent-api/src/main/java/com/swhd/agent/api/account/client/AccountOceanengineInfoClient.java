package com.swhd.agent.api.account.client;

import com.swhd.agent.api.account.dto.param.info.*;
import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.agent.api.common.constant.ApiConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-28
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AccountOceanengineInfoClient.BASE_PATH)
public interface AccountOceanengineInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/account/oceanengine/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<AccountOceanengineInfoResult>> page(@RequestBody @Valid AccountOceanengineInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<AccountOceanengineInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<AccountOceanengineInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据advertiserId列表获取")
    @PostMapping("/listByAdvertiserIds")
    Rsp<List<AccountOceanengineInfoResult>> listByAdvertiserIds(@RequestBody @Valid @NotEmpty Collection<Long> advertiserIds);

    @Operation(summary = "根据advertiserId获取")
    @GetMapping("/getByAdvertiserId")
    Rsp<AccountOceanengineInfoResult> getByAdvertiserId(@RequestParam("advertiserId") Long advertiserId);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid AccountOceanengineInfoUpdateParam param);

    @Operation(summary = "批量修改")
    @PostMapping("/batchUpdate")
    Rsp<Void> batchUpdate(@RequestBody @Valid AccountOceanengineInfoBatchUpdateParam param);

    @Operation(summary = "根据advertiserIds批量修改")
    @PostMapping("/batchUpdateByAdvertiserIds")
    Rsp<Void> batchUpdateByAdvertiserIds(@RequestBody @Valid AccountOceanengineInfoBatchUpdateByAdvertiserIdsParam param);

    @Operation(summary = "根据customIds批量修改")
    @PostMapping("/batchUpdateByCustomId")
    Rsp<Void> batchUpdateByCustomId(@RequestBody @Valid AccountOceanengineInfoBatchUpdateByCustomIdParam param);

}
